import mongoose from 'mongoose';
import User from './Models/user.js';
import dotenv from 'dotenv';

// Load environment variables
dotenv.config();

const createTestUser = async () => {
  try {
    console.log('🔄 Creating test user for recharge testing...');
    
    // Connect to database
    await mongoose.connect(process.env.MONGO_URI);
    console.log('✅ Connected to MongoDB');
    
    // Check if test user already exists
    const existingUser = await User.findOne({ phoneNumber: '9999999999' });
    
    if (existingUser) {
      console.log('✅ Test user already exists, updating points...');
      existingUser.monthlyPoints = 2000;
      existingUser.yearlyPoints = 2000;
      await existingUser.save();
      
      console.log('Updated test user:', {
        id: existingUser._id,
        name: existingUser.fullName,
        phone: existingUser.phoneNumber,
        monthly: existingUser.monthlyPoints,
        yearly: existingUser.yearlyPoints
      });
    } else {
      console.log('🆕 Creating new test user...');
      
      const testUser = new User({
        fullName: 'Test User for Recharge',
        phoneNumber: '9999999999',
        password: 'testpassword123',
        dateOfBirth: new Date('1990-01-01'),
        gender: 'male',
        address: 'Test Address',
        city: 'Test City',
        state: 'Test State',
        pincode: '123456',
        dealerCode: 'TEST001',
        monthlyPoints: 2000,
        yearlyPoints: 2000,
        role: 'Electrician',
        status: 'approved',
        isVerified: true
      });
      
      await testUser.save();
      
      console.log('✅ Test user created:', {
        id: testUser._id,
        name: testUser.fullName,
        phone: testUser.phoneNumber,
        monthly: testUser.monthlyPoints,
        yearly: testUser.yearlyPoints
      });
    }
    
    console.log('✅ Test user ready for recharge testing!');
    
  } catch (error) {
    console.error('❌ Failed to create test user:', error);
  } finally {
    await mongoose.disconnect();
    console.log('📡 Disconnected from MongoDB');
    process.exit(0);
  }
};

createTestUser();
