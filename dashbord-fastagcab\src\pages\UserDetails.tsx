import React, { useState, useEffect } from 'react'
import { use<PERSON>ara<PERSON>, useNavigate } from 'react-router-dom'
import { ArrowLeft, Edit, Trash2, Download, Phone, Mail, MapPin, Calendar, Award } from 'lucide-react'
import Card, { <PERSON><PERSON><PERSON><PERSON>, CardT<PERSON>le, CardContent } from '../components/ui/Card'
import Button from '../components/ui/Button'
import LoadingSpinner from '../components/ui/LoadingSpinner'
import { usersAPI } from '../lib/api'
import { clsx } from 'clsx'
import toast from 'react-hot-toast'
import { format } from 'date-fns'
import getServerBaseUrl from '@/envConfig'
const BackendURL = getServerBaseUrl()

interface User {
  _id: string
  fullName: string
  phoneNumber: string
  role: string
  status: string
  isVerified: boolean
  dateOfBirth: string
  age: number
  adharNumber?: string
  panCardNumber?: string
  pinCode: string
  state: string
  city: string
  address: string
  dealerCode: string
  profilePhoto?: string
  adharCard?: string
  panCard?: string
  bankDetails?: string
  monthlyPoints: number
  yearlyPoints: number
  isVerified: boolean
  createdAt: string
  updatedAt: string
}

const UserDetails: React.FC = () => {
  const { id } = useParams<{ id: string }>()
  const navigate = useNavigate()
  const [user, setUser] = useState<User | null>(null)
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    if (id) {
      fetchUser(id)
    }
  }, [id])

  const fetchUser = async (userId: string) => {
    try {
      setLoading(true)
      const response = await usersAPI.getById(userId)
      
      if (response.success && response.data) {
        setUser(response.data.user)
      }
    } catch (error) {
      console.error('Failed to fetch user:', error)
      toast.error('Failed to load user details')
    } finally {
      setLoading(false)
    }
  }

  const handleDeleteUser = async () => {
    if (!user || !confirm(`Are you sure you want to delete ${user.fullName}?`)) {
      return
    }

    try {
      await usersAPI.delete(user._id)
      toast.success('User deleted successfully')
      navigate('/users')
    } catch (error) {
      console.error('Failed to delete user:', error)
      toast.error('Failed to delete user')
    }
  }

  const getStatusBadge = (status: string) => {
    const statusClasses = {
      pending: 'badge-warning',
      approved: 'badge-success',
      rejected: 'badge-danger',
    }
    
    return (
      <span className={clsx('badge', statusClasses[status as keyof typeof statusClasses] || 'badge-gray')}>
        {status}
      </span>
    )
  }

  const getRoleBadge = (role: string) => {
    const roleClasses = {
      Electrician: 'badge-gray',
      Distributor: 'badge-success',
      admin: 'badge-danger',
    }
    
    return (
      <span className={clsx('badge', roleClasses[role as keyof typeof roleClasses] || 'badge-gray')}>
        {role}
      </span>
    )
  }

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <LoadingSpinner size="lg" />
      </div>
    )
  }

  if (!user) {
    return (
      <div className="text-center py-12">
        <p className="text-gray-500">User not found</p>
        <Button 
          variant="outline" 
          className="mt-4"
          onClick={() => navigate('/users')}
        >
          Back to Users
        </Button>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-4">
          <Button
            variant="outline"
            size="sm"
            onClick={() => navigate('/users')}
          >
            <ArrowLeft className="h-4 w-4 mr-2" />
            Back
          </Button>
          <div>
            <h1 className="text-2xl font-bold text-gray-900">{user.fullName}</h1>
            <p className="text-gray-600">User Details</p>
          </div>
        </div>
        <div className="flex space-x-3">
          <Button variant="outline" size="sm">
            <Edit className="h-4 w-4 mr-2" />
            Edit
          </Button>
          <Button variant="danger" size="sm" onClick={handleDeleteUser}>
            <Trash2 className="h-4 w-4 mr-2" />
            Delete
          </Button>
        </div>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Main Info */}
        <div className="lg:col-span-2 space-y-6">
          {/* Basic Information */}
          <Card>
            <CardHeader>
              <CardTitle>Basic Information</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <label className="label">Full Name</label>
                  <p className="text-gray-900">{user.fullName}</p>
                </div>
                <div>
                  <label className="label">Phone Number</label>
                  <div className="flex items-center">
                    <Phone className="h-4 w-4 mr-2 text-gray-400" />
                    <p className="text-gray-900">{user.phoneNumber}</p>
                  </div>
                </div>
                <div>
                  <label className="label">Role</label>
                  <div>{getRoleBadge(user.role)}</div>
                </div>
                <div>
                  <label className="label">Status</label>
                  <div>{getStatusBadge(user.status)}</div>
                </div>
                <div>
                  <label className="label">Date of Birth</label>
                  <div className="flex items-center">
                    <Calendar className="h-4 w-4 mr-2 text-gray-400" />
                    <p className="text-gray-900">
                      {format(new Date(user.dateOfBirth), 'MMM dd, yyyy')} (Age: {user.age})
                    </p>
                  </div>
                </div>
                <div>
                  <label className="label">Dealer Code</label>
                  <p className="text-gray-900 font-mono">{user.dealerCode}</p>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Address Information */}
          <Card>
            <CardHeader>
              <CardTitle>Address Information</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <label className="label">Address</label>
                  <div className="flex items-start">
                    <MapPin className="h-4 w-4 mr-2 text-gray-400 mt-1" />
                    <p className="text-gray-900">{user.address}</p>
                  </div>
                </div>
                <div>
                  <label className="label">City</label>
                  <p className="text-gray-900">{user.city}</p>
                </div>
                <div>
                  <label className="label">State</label>
                  <p className="text-gray-900">{user.state}</p>
                </div>
                <div>
                  <label className="label">PIN Code</label>
                  <p className="text-gray-900">{user.pinCode}</p>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Documents */}
          <Card>
            <CardHeader>
              <CardTitle>Documents</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <label className="label">Adhar Number</label>
                  <p className="text-gray-900">{user.adharNumber || 'Not provided'}</p>
                </div>
                <div>
                  <label className="label">PAN Card Number</label>
                  <p className="text-gray-900">{user.panCardNumber || 'Not provided'}</p>
                </div>
              </div>
              
              <div className="mt-6">
                <label className="label">Uploaded Documents</label>
                <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mt-2">
                  {user.profilePhoto && (
                    <div className="text-center">
                      <div className="w-16 h-16 bg-gray-100 rounded-lg flex items-center justify-center mb-2">
                        <img 
                          src={`${BackendURL}/uploads/profiles/${user.profilePhoto}`}
                          alt="Profile"
                          className="w-full h-full object-cover rounded-lg"
                        />
                      </div>
                      <p className="text-xs text-gray-600">Profile Photo</p>
                    </div>
                  )}
                  {user.adharCard && (
                    <div className="text-center">
                      <div className="w-16 h-16 bg-gray-100 rounded-lg flex items-center justify-center mb-2">
                        <Download className="h-6 w-6 text-gray-400" />
                      </div>
                      <p className="text-xs text-gray-600">Adhar Card</p>
                    </div>
                  )}
                  {user.panCard && (
                    <div className="text-center">
                      <div className="w-16 h-16 bg-gray-100 rounded-lg flex items-center justify-center mb-2">
                        <Download className="h-6 w-6 text-gray-400" />
                      </div>
                      <p className="text-xs text-gray-600">PAN Card</p>
                    </div>
                  )}
                  {user.bankDetails && (
                    <div className="text-center">
                      <div className="w-16 h-16 bg-gray-100 rounded-lg flex items-center justify-center mb-2">
                        <Download className="h-6 w-6 text-gray-400" />
                      </div>
                      <p className="text-xs text-gray-600">Bank Details</p>
                    </div>
                  )}
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Sidebar */}
        <div className="space-y-6">
          {/* Points */}
          <Card>
            <CardHeader>
              <CardTitle>Points Summary</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <div className="flex items-center">
                    <Award className="h-5 w-5 text-yellow-500 mr-2" />
                    <span className="text-sm font-medium">Monthly Points</span>
                  </div>
                  <span className="text-lg font-bold text-gray-900">
                    {user.monthlyPoints || 0}
                  </span>
                </div>
                <div className="flex items-center justify-between">
                  <div className="flex items-center">
                    <Award className="h-5 w-5 text-green-500 mr-2" />
                    <span className="text-sm font-medium">Yearly Points</span>
                  </div>
                  <span className="text-lg font-bold text-gray-900">
                    {user.yearlyPoints || 0}
                  </span>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Account Info */}
          <Card>
            <CardHeader>
              <CardTitle>Account Information</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div>
                  <label className="label">Verified</label>
                  <p className={clsx(
                    'text-sm font-medium',
                    user.isVerified ? 'text-green-600' : 'text-red-600'
                  )}>
                    {user.isVerified ? 'Yes' : 'No'}
                  </p>
                </div>
                <div>
                  <label className="label">Registered</label>
                  <p className="text-sm text-gray-900">
                    {format(new Date(user.createdAt), 'MMM dd, yyyy')}
                  </p>
                </div>
                <div>
                  <label className="label">Last Updated</label>
                  <p className="text-sm text-gray-900">
                    {format(new Date(user.updatedAt), 'MMM dd, yyyy')}
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  )
}

export default UserDetails
