import { validationResult } from 'express-validator';
import User from '../Models/user.js';
import Recharge from '../Models/Recharge.js';
import Notification from '../Models/Notification.js';
import PointsService from '../services/pointsService.js';
import { getFileUrl } from '../utils/fileHelper.js';

// @desc    Get all users
// @route   GET api/users
// @access  Private (Admin only)
export const getAllUsers = async (req, res) => {
    try {
        const page = parseInt(req.query.page) || 1;
        const limit = parseInt(req.query.limit) || 10;
        const skip = (page - 1) * limit;

        const users = await User.find()
            .select('-password')
            .skip(skip)
            .limit(limit)
            .sort({ createdAt: -1 });

        const totalUsers = await User.countDocuments();
        const totalPages = Math.ceil(totalUsers / limit);

        res.status(200).json({
            success: true,
            message: 'Users retrieved successfully',
            data: {
                users,
                pagination: {
                    currentPage: page,
                    totalPages,
                    totalUsers,
                    hasNextPage: page < totalPages,
                    hasPrevPage: page > 1
                }
            }
        });

    } catch (error) {
        console.error('Get all users error:', error);
        res.status(500).json({
            success: false,
            message: 'Server error while fetching users'
        });
    }
};

// @desc    Get single user by ID
// @route   GET api/users/:id
// @access  Private (Own profile or Admin)
export const getUserById = async (req, res) => {
    try {
        const { id } = req.params;

        const user = await User.findById(id).select('-password');

        if (!user) {
            return res.status(404).json({
                success: false,
                message: 'User not found'
            });
        }

        // Get updated points balances with reset check
        const pointBalances = await PointsService.getUserPointBalances(id);

        // Merge user data with updated points
        const userData = {
            ...user.toObject(),
            monthlyPoints: pointBalances.monthlyPoints,
            yearlyPoints: pointBalances.yearlyPoints,
            pointsInfo: {
                lastMonthlyReset: pointBalances.lastMonthlyReset,
                nextMonthlyReset: pointBalances.nextMonthlyReset,
                nextYearlyReset: pointBalances.nextYearlyReset,
                registrationAnniversary: pointBalances.registrationAnniversary
            }
        };

        res.status(200).json({
            success: true,
            message: 'User retrieved successfully',
            data: { user: userData }
        });

    } catch (error) {
        console.error('Get user by ID error:', error);

        if (error.name === 'CastError') {
            return res.status(400).json({
                success: false,
                message: 'Invalid user ID format'
            });
        }

        res.status(500).json({
            success: false,
            message: 'Server error while fetching user'
        });
    }
};

// @desc    Update user details
// @route   PUT api/users/:id
// @access  Private (Own profile or Admin)
export const updateUser = async (req, res) => {
    try {
        // Check for validation errors
        const errors = validationResult(req);
        if (!errors.isEmpty()) {
            return res.status(400).json({
                success: false,
                message: 'Validation failed',
                errors: errors.array()
            });
        }

        const { id } = req.params;
        const {
            fullName,
            phoneNumber,
            dateOfBirth,
            age,
            adharNumber,
            panCardNumber,
            pinCode,
            state,
            city,
            address,
            dealerCode,
            role,
            status
        } = req.body;

        // Check if user exists
        const user = await User.findById(id);
        if (!user) {
            return res.status(404).json({
                success: false,
                message: 'User not found'
            });
        }

        // Check if phone number is being changed and if it's already taken
        if (phoneNumber && phoneNumber !== user.phoneNumber) {
            const existingUser = await User.findOne({ phoneNumber });
            if (existingUser) {
                return res.status(400).json({
                    success: false,
                    message: 'Phone number already exists'
                });
            }
        }

        // Only admin can change roles and status
        if ((role || status) && req.user.role !== 'admin') {
            return res.status(403).json({
                success: false,
                message: 'Only admin can change user roles and status'
            });
        }

        // Prepare update data
        const updateData = {};
        if (fullName) updateData.fullName = fullName;
        if (phoneNumber) updateData.phoneNumber = phoneNumber;
        if (dateOfBirth) updateData.dateOfBirth = new Date(dateOfBirth);
        if (age) updateData.age = parseInt(age);
        if (adharNumber) updateData.adharNumber = adharNumber;
        if (panCardNumber) updateData.panCardNumber = panCardNumber;
        if (pinCode) updateData.pinCode = pinCode;
        if (state) updateData.state = state;
        if (city) updateData.city = city;
        if (address) updateData.address = address;
        if (dealerCode) updateData.dealerCode = dealerCode;
        if (role) updateData.role = role;
        if (status) updateData.status = status;

        // Update user
        const updatedUser = await User.findByIdAndUpdate(
            id,
            updateData,
            {
                new: true,
                runValidators: true
            }
        ).select('-password');

        res.status(200).json({
            success: true,
            message: 'User updated successfully',
            data: { user: updatedUser }
        });

    } catch (error) {
        console.error('Update user error:', error);

        if (error.name === 'CastError') {
            return res.status(400).json({
                success: false,
                message: 'Invalid user ID format'
            });
        }

        res.status(500).json({
            success: false,
            message: 'Server error while updating user'
        });
    }
};

// @desc    Delete user
// @route   DELETE api/users/:id
// @access  Private (Own profile or Admin)
export const deleteUser = async (req, res) => {
    try {
        const { id } = req.params;

        // Check if user exists
        const user = await User.findById(id);
        if (!user) {
            return res.status(404).json({
                success: false,
                message: 'User not found'
            });
        }

        // Prevent users from deleting admin accounts (unless they are admin themselves)
        if (user.role === 'admin' && req.user.role !== 'admin') {
            return res.status(403).json({
                success: false,
                message: 'Cannot delete admin user'
            });
        }

        // Delete user
        await User.findByIdAndDelete(id);

        res.status(200).json({
            success: true,
            message: 'User deleted successfully'
        });

    } catch (error) {
        console.error('Delete user error:', error);

        if (error.name === 'CastError') {
            return res.status(400).json({
                success: false,
                message: 'Invalid user ID format'
            });
        }

        res.status(500).json({
            success: false,
            message: 'Server error while deleting user'
        });
    }
};

// @desc    Update user points
// @route   PUT api/users/:id/points
// @access  Private (Own profile or Admin)
export const updateUserPoints = async (req, res) => {
    try {
        // Check for validation errors
        const errors = validationResult(req);
        if (!errors.isEmpty()) {
            return res.status(400).json({
                success: false,
                message: 'Validation failed',
                errors: errors.array()
            });
        }

        const { id } = req.params;
        const { monthlyPoints, yearlyPoints } = req.body;

        // Check if user exists
        const user = await User.findById(id);
        if (!user) {
            return res.status(404).json({
                success: false,
                message: 'User not found'
            });
        }

        // Prepare update data
        const updateData = {};
        if (monthlyPoints !== undefined) updateData.monthlyPoints = monthlyPoints;
        if (yearlyPoints !== undefined) updateData.yearlyPoints = yearlyPoints;

        // Update user points
        const updatedUser = await User.findByIdAndUpdate(
            id,
            updateData,
            {
                new: true,
                runValidators: true
            }
        ).select('-password');

        res.status(200).json({
            success: true,
            message: 'User points updated successfully',
            data: {
                user: updatedUser,
                monthlyPoints: updatedUser.monthlyPoints,
                yearlyPoints: updatedUser.yearlyPoints
            }
        });

    } catch (error) {
        console.error('Update user points error:', error);

        if (error.name === 'CastError') {
            return res.status(400).json({
                success: false,
                message: 'Invalid user ID format'
            });
        }

        res.status(500).json({
            success: false,
            message: 'Server error while updating user points'
        });
    }
};

// @desc    Process recharge request
// @route   POST api/users/recharge
// @access  Private
// @desc    Process recharge request
// @route   POST api/users/recharge
// @access  Private
export const processRecharge = async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({ success: false, message: 'Validation failed', errors: errors.array() });
    }

    const userId = req.user._id;
    const { mobileNumber, operator, pointsToDeduct, rechargeAmount, rechargeDuration, rechargeType = 'monthly_plan' } = req.body;

    const user = await User.findById(userId);
    if (!user) {
      return res.status(404).json({ success: false, message: 'User not found' });
    }

    const userMonthlyPointsAtRecharge = user.monthlyPoints;

    let updatedUser;
    try {
      const pointsResult = await PointsService.deductPointsForRecharge(userId, pointsToDeduct, {
        mobileNumber: mobileNumber.trim(),
        operator: operator.trim(),
        rechargeAmount,
        rechargeDuration,
        rechargeType,
      });
      updatedUser = pointsResult.user;
    } catch (pointsError) {
      return res.status(400).json({ success: false, message: pointsError.message });
    }

    // Deduct points from user
    user.monthlyPoints -= pointsToDeduct;
    user.yearlyPoints -= pointsToDeduct;

    const recharge = new Recharge({
      userId,
      mobileNumber: mobileNumber.trim(),
      operator: operator.trim(),
      rechargeAmount,
      rechargeDuration,
      rechargeType,
      pointsDeducted: pointsToDeduct,
      userMonthlyPointsAtRecharge,
      status: 'pending',
      metadata: {
        userAgent: req.get('User-Agent'),
        ipAddress: req.ip,
        deviceInfo: req.get('X-Device-Info') || 'Unknown',
        requestSource: 'mobile_app',
      },
    });
    await recharge.save();

    const userNotification = new Notification({
      userId,
      title: 'Recharge Request Submitted',
      message: `Your recharge for ${mobileNumber} (${rechargeAmount}) has been submitted; ${pointsToDeduct} points deducted from both monthly & yearly balances.`,
      type: 'info',
      priority: 'medium',
      data: { rechargeId: recharge._id, mobileNumber, operator, rechargeAmount, pointsDeducted: pointsToDeduct, type: 'recharge_submitted' },
      actionUrl: '/recharge-history',
    });
    await userNotification.save();

    const dashboardNotification = new Notification({
      title: 'New Recharge Request',
      message: `New recharge request from ${user.fullName} for ${mobileNumber} (${rechargeAmount})`,
      type: 'info',
      priority: 'medium',
      isGlobal: true,
      data: { rechargeId: recharge._id, userId, userName: user.fullName, mobileNumber, operator, rechargeAmount, pointsDeducted: pointsToDeduct, type: 'recharge_request' },
      actionUrl: `/admin/recharges/${recharge._id}`,
    });
    await dashboardNotification.save();

    recharge.userNotificationId = userNotification._id;
    recharge.dashboardNotificationId = dashboardNotification._id;
    recharge.userNotificationSent = true;
    recharge.dashboardNotificationSent = true;
    await recharge.save();

    console.log('Recharge request processed:', {
      rechargeId: recharge._id,
      transactionId: recharge.transactionId,
      mobileNumber,
      operator,
      rechargeAmount,
      pointsDeducted: pointsToDeduct,
    });

    res.status(200).json({
      success: true,
      message: `Recharge request submitted successfully! ${pointsToDeduct} points deducted from both monthly and yearly balances.`,
      data: {
        recharge: {
          _id: recharge._id,
          transactionId: recharge.transactionId,
          mobileNumber: recharge.mobileNumber,
          operator: recharge.operator,
          rechargeAmount: recharge.rechargeAmount,
          rechargeDuration: recharge.rechargeDuration,
          pointsDeducted: recharge.pointsDeducted,
          status: recharge.status,
          rechargeDate: recharge.rechargeDate,
        },
        remainingPoints: {
          monthlyPoints: updatedUser.monthlyPoints,
          yearlyPoints: updatedUser.yearlyPoints,
        },
        user: {
          _id: updatedUser._id,
          fullName: updatedUser.fullName,
          monthlyPoints: updatedUser.monthlyPoints,
          yearlyPoints: updatedUser.yearlyPoints,
        },
      },
    });
  } catch (error) {
    console.error('Process recharge error:', error);
    res.status(500).json({ success: false, message: 'Server error while processing recharge' });
  }
};

// @desc    Redeem gift
// @route   POST api/users/redeem-gift
// @access  Private
export const redeemGift = async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({ success: false, message: 'Validation failed', errors: errors.array() });
    }

    const userId = req.user._id;
    const { giftId, pointsRequired } = req.body;

    const user = await User.findById(userId);
    if (!user) {
      return res.status(404).json({ success: false, message: 'User not found' });
    }

    // Only check yearly points for gift redemption
    if (user.yearlyPoints < pointsRequired) {
      return res.status(400).json({
        success: false,
        message: `Insufficient yearly points. You have ${user.yearlyPoints} yearly points but need ${pointsRequired} points.`
      });
    }

    // TODO: Implement gift redemption logic (e.g., create a gift redemption record, update user points, etc.)

    res.status(200).json({
      success: true,
      message: 'Gift redeemed successfully',
      data: {
        // Include any relevant data for the redeemed gift
      },
    });
  } catch (error) {
    console.error('Redeem gift error:', error);
    res.status(500).json({ success: false, message: 'Server error while redeeming gift' });
  }
};
