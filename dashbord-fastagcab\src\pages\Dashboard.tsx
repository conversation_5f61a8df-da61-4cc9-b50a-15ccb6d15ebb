import React, { useState, useEffect } from 'react'
import { Users, QrCode, TrendingUp, Award, RefreshCw, AlertCircle } from 'lucide-react'
import Card, { CardHeader, CardTitle, CardContent } from '../components/ui/Card'
import Button from '../components/ui/Button'
import LoadingSpinner from '../components/ui/LoadingSpinner'
import { usersAPI, analyticsAPI } from '../lib/api'
import { BarChart, Bar, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer, PieChart, Pie, Cell } from 'recharts'
import toast from 'react-hot-toast'

interface DashboardStats {
  totalUsers: number
  totalElectricians: number
  totalDistributors: number
  pendingApprovals: number
  totalPoints: number
  monthlyRegistrations: Array<{ month: string; count: number }>
  usersByRole: Array<{ name: string; value: number; color: string }>
}

const Dashboard: React.FC = () => {
  const [stats, setStats] = useState<DashboardStats | null>(null)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  useEffect(() => {
    fetchDashboardData()
  }, [])

  const fetchDashboardData = async () => {
    try {
      setLoading(true)
      setError(null)

      // Fetch dashboard stats from admin API
      const statsResponse = await analyticsAPI.getDashboardStats()

      if (statsResponse.success && statsResponse.data) {
        const data = statsResponse.data

        // Mock monthly registrations data (you can implement this in backend)
        const monthlyRegistrations = [
          { month: 'Jan', count: 45 },
          { month: 'Feb', count: 52 },
          { month: 'Mar', count: 48 },
          { month: 'Apr', count: 61 },
          { month: 'May', count: 55 },
          { month: 'Jun', count: 67 },
        ]

        const usersByRole = [
          { name: 'Electricians', value: data.totalElectricians, color: '#3b82f6' },
          { name: 'Distributors', value: data.totalDistributors, color: '#10b981' },
          { name: 'Admins', value: data.totalUsers - data.totalElectricians - data.totalDistributors, color: '#f59e0b' },
        ]

        setStats({
          totalUsers: data.totalUsers,
          totalElectricians: data.totalElectricians,
          totalDistributors: data.totalDistributors,
          pendingApprovals: data.pendingApprovals,
          totalPoints: data.totalMonthlyPoints,
          monthlyRegistrations,
          usersByRole,
        })
        toast.success('Dashboard data loaded successfully')
      }
    } catch (error: any) {
      console.error('Failed to fetch dashboard data:', error)

      // Fallback to users API if admin API fails
      try {
        const response = await usersAPI.getAll(1, 1000)
        if (response.success && response.data) {
          const users = response.data.users

          const totalUsers = users.length
          const totalElectricians = users.filter((u: any) => u.role === 'Electrician').length
          const totalDistributors = users.filter((u: any) => u.role === 'Distributor').length
          const pendingApprovals = users.filter((u: any) => u.status === 'pending').length
          const totalPoints = users.reduce((sum: number, u: any) => sum + (u.monthlyPoints || 0), 0)

          const monthlyRegistrations = [
            { month: 'Jan', count: 45 },
            { month: 'Feb', count: 52 },
            { month: 'Mar', count: 48 },
            { month: 'Apr', count: 61 },
            { month: 'May', count: 55 },
            { month: 'Jun', count: 67 },
          ]

          const usersByRole = [
            { name: 'Electricians', value: totalElectricians, color: '#3b82f6' },
            { name: 'Distributors', value: totalDistributors, color: '#10b981' },
            { name: 'Admins', value: users.filter((u: any) => u.role === 'admin').length, color: '#f59e0b' },
          ]

          setStats({
            totalUsers,
            totalElectricians,
            totalDistributors,
            pendingApprovals,
            totalPoints,
            monthlyRegistrations,
            usersByRole,
          })
          toast.success('Dashboard data loaded (fallback mode)')
        }
      } catch (fallbackError: any) {
        console.error('Fallback API also failed:', fallbackError)
        const message = fallbackError.response?.data?.message || 'Failed to load dashboard data'
        setError(message)
        toast.error(message)
      }
    } finally {
      setLoading(false)
    }
  }

  if (loading) {
    return (
      <div className="space-y-6">
        <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
          <div>
            <h1 className="text-3xl font-bold text-gray-900">Dashboard</h1>
            <p className="text-gray-600 mt-1">Overview of your FastagCab system</p>
          </div>
        </div>
        <div className="flex flex-col items-center justify-center h-64 space-y-4">
          <LoadingSpinner size="lg" />
          <p className="text-gray-500">Loading dashboard data...</p>
        </div>
      </div>
    )
  }

  if (error || !stats) {
    return (
      <div className="space-y-6">
        <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
          <div>
            <h1 className="text-3xl font-bold text-gray-900">Dashboard</h1>
            <p className="text-gray-600 mt-1">Overview of your FastagCab system</p>
          </div>
          <Button
            onClick={fetchDashboardData}
            variant="outline"
            className="flex items-center gap-2"
          >
            <RefreshCw className="h-4 w-4" />
            Retry
          </Button>
        </div>
        <div className="flex flex-col items-center justify-center h-64 space-y-4">
          <AlertCircle className="h-12 w-12 text-red-500" />
          <div className="text-center">
            <p className="text-gray-900 font-medium">Failed to load dashboard data</p>
            <p className="text-gray-500 mt-1">{error || 'Please try again'}</p>
          </div>
        </div>
      </div>
    )
  }

  const statCards = [
    {
      title: 'Total Users',
      value: stats.totalUsers,
      icon: Users,
      color: 'text-blue-600',
      bgColor: 'bg-blue-100',
    },
    {
      title: 'Pending Approvals',
      value: stats.pendingApprovals,
      icon: QrCode,
      color: 'text-yellow-600',
      bgColor: 'bg-yellow-100',
    },
    {
      title: 'Total Points',
      value: stats.totalPoints.toLocaleString(),
      icon: Award,
      color: 'text-green-600',
      bgColor: 'bg-green-100',
    },
    {
      title: 'Monthly Growth',
      value: '+12%',
      icon: TrendingUp,
      color: 'text-purple-600',
      bgColor: 'bg-purple-100',
    },
  ]

  return (
    <div className="space-y-6">
      {/* Page Header */}
      <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Dashboard</h1>
          <p className="text-gray-600 mt-1">Overview of your FastagCab system</p>
        </div>
        <Button
          onClick={fetchDashboardData}
          variant="outline"
          disabled={loading}
          className="flex items-center gap-2"
        >
          <RefreshCw className={`h-4 w-4 ${loading ? 'animate-spin' : ''}`} />
          Refresh
        </Button>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        {statCards.map((stat, index) => (
          <Card key={index} className="hover:shadow-medium transition-shadow">
            <div className="flex items-center">
              <div className={`p-3 rounded-lg ${stat.bgColor}`}>
                <stat.icon className={`h-6 w-6 ${stat.color}`} />
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600">{stat.title}</p>
                <p className="text-2xl font-bold text-gray-900">{stat.value}</p>
              </div>
            </div>
          </Card>
        ))}
      </div>

      {/* Charts */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Monthly Registrations Chart */}
        <Card>
          <CardHeader>
            <CardTitle>Monthly Registrations</CardTitle>
          </CardHeader>
          <CardContent>
            <ResponsiveContainer width="100%" height={300}>
              <BarChart data={stats.monthlyRegistrations}>
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis dataKey="month" />
                <YAxis />
                <Tooltip />
                <Bar dataKey="count" fill="#3b82f6" />
              </BarChart>
            </ResponsiveContainer>
          </CardContent>
        </Card>

        {/* Users by Role Chart */}
        <Card>
          <CardHeader>
            <CardTitle>Users by Role</CardTitle>
          </CardHeader>
          <CardContent>
            <ResponsiveContainer width="100%" height={300}>
              <PieChart>
                <Pie
                  data={stats.usersByRole}
                  cx="50%"
                  cy="50%"
                  labelLine={false}
                  label={({ name, percent }) => `${name} ${(percent * 100).toFixed(0)}%`}
                  outerRadius={80}
                  fill="#8884d8"
                  dataKey="value"
                >
                  {stats.usersByRole.map((entry, index) => (
                    <Cell key={`cell-${index}`} fill={entry.color} />
                  ))}
                </Pie>
                <Tooltip />
              </PieChart>
            </ResponsiveContainer>
          </CardContent>
        </Card>
      </div>

      {/* Recent Activity */}
      <Card>
        <CardHeader>
          <CardTitle>Recent Activity</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <div className="flex items-center space-x-4">
              <div className="w-2 h-2 bg-green-500 rounded-full"></div>
              <div className="flex-1">
                <p className="text-sm font-medium text-gray-900">New user registered</p>
                <p className="text-xs text-gray-500">2 minutes ago</p>
              </div>
            </div>
            <div className="flex items-center space-x-4">
              <div className="w-2 h-2 bg-blue-500 rounded-full"></div>
              <div className="flex-1">
                <p className="text-sm font-medium text-gray-900">QR code redeemed</p>
                <p className="text-xs text-gray-500">5 minutes ago</p>
              </div>
            </div>
            <div className="flex items-center space-x-4">
              <div className="w-2 h-2 bg-yellow-500 rounded-full"></div>
              <div className="flex-1">
                <p className="text-sm font-medium text-gray-900">User approval pending</p>
                <p className="text-xs text-gray-500">10 minutes ago</p>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}

export default Dashboard
