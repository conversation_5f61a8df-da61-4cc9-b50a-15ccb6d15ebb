import User from '../Models/user.js';
import PointHistory from '../Models/PointHistory.js';
import mongoose from 'mongoose';

class PointsService {
  /**
   * Validate points input
   * @param {number} points - Points to validate
   * @param {string} operation - Operation type (add/deduct)
   */
  static validatePoints(points, operation = 'add') {
    if (typeof points !== 'number' || isNaN(points)) {
      throw new Error('Points must be a valid number');
    }

    if (points < 0) {
      throw new Error('Points cannot be negative');
    }

    if (points === 0) {
      throw new Error('Points cannot be zero');
    }

    if (points > 1000000) {
      throw new Error('Points cannot exceed 1,000,000');
    }

    if (!Number.isInteger(points)) {
      throw new Error('Points must be a whole number');
    }
  }

  /**
   * Validate user ID
   * @param {string} userId - User ID to validate
   */
  static validateUserId(userId) {
    if (!userId) {
      throw new Error('User ID is required');
    }

    if (!mongoose.Types.ObjectId.isValid(userId)) {
      throw new Error('Invalid user ID format');
    }
  }

  /**
   * Add points to user (both monthly and yearly)
   * @param {string} userId - User ID
   * @param {number} points - Points to add
   * @param {string} source - Source of points
   * @param {Object} metadata - Additional metadata
   * @returns {Promise<Object>} Updated user and point history entry
   */
  static async addPoints(userId, points, source, metadata = {}) {
    // Validate inputs
    this.validateUserId(userId);
    this.validatePoints(points, 'add');

    if (!source || typeof source !== 'string') {
      throw new Error('Source is required and must be a string');
    }

    const session = await mongoose.startSession();
    session.startTransaction();

    try {
      const user = await User.findById(userId).session(session);
      if (!user) {
        throw new Error('User not found');
      }

      // Check if monthly reset is needed before adding points
      if (user.needsMonthlyReset) {
        await this.performMonthlyReset(user, session);
      }

      // Add points to both monthly and yearly
      user.addPoints(points);
      await user.save({ session });

      // Create point history entry
      const pointHistoryEntry = await PointHistory.createEntry({
        userId,
        transactionType: 'earned',
        pointsChange: points,
        pointsBalance: user.monthlyPoints, // Use monthly points as primary balance
        source,
        description: `Earned ${points} points from ${source}`,
        metadata: {
          ...metadata,
          monthlyPointsAfter: user.monthlyPoints,
          yearlyPointsAfter: user.yearlyPoints
        }
      });

      await session.commitTransaction();

      return {
        success: true,
        user,
        pointHistory: pointHistoryEntry,
        pointsAdded: points,
        newMonthlyBalance: user.monthlyPoints,
        newYearlyBalance: user.yearlyPoints
      };
    } catch (error) {
      await session.abortTransaction();
      throw error;
    } finally {
      session.endSession();
    }
  }

  /**
   * Deduct points from user with different logic for recharge vs gifts
   * @param {string} userId - User ID
   * @param {number} points - Points to deduct
   * @param {string} source - Source of deduction (gift_redemption, recharge, etc.)
   * @param {Object} metadata - Additional metadata
   * @param {string} redemptionType - Type of redemption ('recharge' or 'gift')
   * @returns {Promise<Object>} Updated user and point history entry
   */
  static async deductPoints(userId, points, source, metadata = {}, redemptionType = 'gift') {
    // Validate inputs
    this.validateUserId(userId);
    this.validatePoints(points, 'deduct');

    if (!source || typeof source !== 'string') {
      throw new Error('Source is required and must be a string');
    }

    const session = await mongoose.startSession();
    session.startTransaction();

    try {
      const user = await User.findById(userId).session(session);
      if (!user) {
        throw new Error('User not found');
      }

      // Check if monthly reset is needed before deducting points
      if (user.needsMonthlyReset) {
        await this.performMonthlyReset(user, session);
      }

      // Check if user has sufficient points based on redemption type
      const pointCheckType = redemptionType === 'recharge' ? 'recharge' : 'gift';
      if (!user.hasSufficientPoints(points, pointCheckType, redemptionType)) {
        let errorMessage;
        if (redemptionType === 'recharge') {
          if (user.monthlyPoints < 500) {
            errorMessage = `Minimum 500 monthly points required for recharge. You have ${user.monthlyPoints} monthly points.`;
          } else {
            errorMessage = `Insufficient monthly points for recharge. Required: ${points}, Available: ${user.monthlyPoints}`;
          }
        } else {
          errorMessage = `Insufficient yearly points for gift redemption. Required: ${points}, Available: ${user.yearlyPoints}`;
        }

        const error = new Error(errorMessage);
        error.code = 'INSUFFICIENT_POINTS';
        error.details = {
          required: points,
          available: {
            monthly: user.monthlyPoints,
            yearly: user.yearlyPoints,
            total: user.monthlyPoints + user.yearlyPoints
          },
          redemptionType
        };
        throw error;
      }

      // Store original balances for history
      const originalMonthlyPoints = user.monthlyPoints;
      const originalYearlyPoints = user.yearlyPoints;

      // Deduct points based on redemption type
      user.deductPoints(points, redemptionType);
      const deductionDetails = user._lastDeduction;
      await user.save({ session });

      // Create point history entry with detailed deduction info
      const pointHistoryEntry = await PointHistory.createEntry({
        userId,
        transactionType: 'redeemed',
        pointsChange: -points,
        pointsBalance: user.monthlyPoints, // Use monthly points as primary balance
        source,
        description: `Redeemed ${points} points for ${source} (${deductionDetails.deductedFromMonthly} from monthly, ${deductionDetails.deductedFromYearly} from yearly)`,
        metadata: {
          ...metadata,
          monthlyPointsBefore: originalMonthlyPoints,
          yearlyPointsBefore: originalYearlyPoints,
          monthlyPointsAfter: user.monthlyPoints,
          yearlyPointsAfter: user.yearlyPoints,
          deductionDetails: deductionDetails
        }
      });

      await session.commitTransaction();

      return {
        success: true,
        user,
        pointHistory: pointHistoryEntry,
        pointsDeducted: points,
        newMonthlyBalance: user.monthlyPoints,
        newYearlyBalance: user.yearlyPoints,
        deductionDetails: deductionDetails
      };
    } catch (error) {
      await session.abortTransaction();
      throw error;
    } finally {
      session.endSession();
    }
  }

  /**
   * Perform monthly reset for a user
   * @param {Object} user - User document
   * @param {Object} session - MongoDB session
   * @returns {Promise<void>}
   */
  static async performMonthlyReset(user, session = null) {
    const originalMonthlyPoints = user.monthlyPoints;
    
    // Reset monthly points but keep yearly points
    await user.resetMonthlyPoints();
    
    if (session) {
      await user.save({ session });
    }

    // Create point history entry for the reset
    await PointHistory.createEntry({
      userId: user._id,
      transactionType: 'expired',
      pointsChange: -originalMonthlyPoints,
      pointsBalance: 0, // Monthly points are now 0
      source: 'monthly_reset',
      description: `Monthly points reset: ${originalMonthlyPoints} points expired`,
      metadata: {
        resetDate: new Date(),
        expiredMonthlyPoints: originalMonthlyPoints,
        yearlyPointsRemaining: user.yearlyPoints
      }
    });
  }

  /**
   * Perform yearly reset for a user (DISABLED - yearly points don't auto-reset)
   * @param {Object} user - User document
   * @param {Object} session - MongoDB session
   * @returns {Promise<void>}
   */
  static async performYearlyReset(user, session = null) {
    // DO NOT reset yearly points automatically
    // Yearly points should only be reduced through redemption
    console.log('WARNING: performYearlyReset called but yearly points should not auto-reset');
    return Promise.resolve();
  }

  /**
   * Deduct points specifically for recharge redemption
   * @param {string} userId - User ID
   * @param {number} points - Points to deduct
   * @param {Object} metadata - Additional metadata
   * @returns {Promise<Object>} Updated user and point history entry
   */
  static async deductPointsForRecharge(userId, points, metadata = {}) {
    return this.deductPoints(userId, points, 'recharge', metadata, 'recharge');
  }

  /**
   * Deduct points specifically for gift redemption
   * @param {string} userId - User ID
   * @param {number} points - Points to deduct
   * @param {Object} metadata - Additional metadata
   * @returns {Promise<Object>} Updated user and point history entry
   */
  static async deductPointsForGift(userId, points, metadata = {}) {
    return this.deductPoints(userId, points, 'gift_redemption', metadata, 'gift');
  }

  /**
   * Get user's current point balances with reset check
   * @param {string} userId - User ID
   * @returns {Promise<Object>} User's point balances
   */
  static async getUserPointBalances(userId) {
    const user = await User.findById(userId);
    if (!user) {
      throw new Error('User not found');
    }

    // Check if monthly reset is needed
    if (user.needsMonthlyReset) {
      await this.performMonthlyReset(user);
    }

    // Check if yearly reset is needed
    if (user.needsYearlyReset) {
      await this.performYearlyReset(user);
    }

    return {
      monthlyPoints: user.monthlyPoints,
      yearlyPoints: user.yearlyPoints,
      lastMonthlyReset: user.lastMonthlyReset,
      nextMonthlyReset: user.nextMonthlyReset,
      nextYearlyReset: user.nextYearlyReset,
      registrationAnniversary: user.registrationAnniversary
    };
  }

  /**
   * Process all users for monthly reset (for scheduled task)
   * @returns {Promise<Object>} Reset statistics
   */
  static async processMonthlyResetForAllUsers() {
    const users = await User.find({
      $or: [
        { lastMonthlyReset: null },
        { lastMonthlyReset: { $lt: new Date(new Date().getFullYear(), new Date().getMonth(), 1) } }
      ]
    });

    let resetCount = 0;
    let totalPointsExpired = 0;
    const errors = [];

    for (const user of users) {
      try {
        if (user.needsMonthlyReset) {
          const originalPoints = user.monthlyPoints;
          await this.performMonthlyReset(user);
          resetCount++;
          totalPointsExpired += originalPoints;
        }
      } catch (error) {
        errors.push({
          userId: user._id,
          error: error.message
        });
      }
    }

    return {
      totalUsersProcessed: users.length,
      usersReset: resetCount,
      totalPointsExpired,
      errors
    };
  }

  /**
   * Process all users for yearly reset (for scheduled task)
   * @returns {Promise<Object>} Reset statistics
   */
  static async processYearlyResetForAllUsers() {
    const users = await User.find({
      yearlyPointsResetAt: { $lte: new Date() }
    });

    let resetCount = 0;
    let totalPointsExpired = 0;
    const errors = [];

    for (const user of users) {
      try {
        if (user.needsYearlyReset) {
          const originalPoints = user.yearlyPoints;
          await this.performYearlyReset(user);
          resetCount++;
          totalPointsExpired += originalPoints;
        }
      } catch (error) {
        errors.push({
          userId: user._id,
          error: error.message
        });
      }
    }

    return {
      totalUsersProcessed: users.length,
      usersReset: resetCount,
      totalPointsExpired,
      errors
    };
  }
}

export default PointsService;
