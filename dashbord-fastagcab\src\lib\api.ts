import axios from 'axios'
import toast from 'react-hot-toast'

// Create axios instance with base configuration
const api = axios.create({
  baseURL: (import.meta as any).env?.VITE_API_URL || 'http://localhost:3000/api',
  timeout: 30000,
  headers: {
    'Content-Type': 'application/json',
  },
})

// Request interceptor to add auth token
api.interceptors.request.use(
  (config) => {
    const token = localStorage.getItem('admin_token')
    if (token) {
      config.headers.Authorization = `Bearer ${token}`
    }
    return config
  },
  (error) => {
    return Promise.reject(error)
  }
)

// Response interceptor for error handling
api.interceptors.response.use(
  (response) => response,
  (error) => {
    if (error.response?.status === 401) {
      localStorage.removeItem('admin_token')
      localStorage.removeItem('admin_user')
      window.location.href = '/login'
      toast.error('Session expired. Please login again.')
    } else if (error.response?.status === 403) {
      toast.error('Access denied. Admin privileges required.')
    } else if (error.response?.status >= 500) {
      toast.error('Server error. Please try again later.')
    } else if (error.code === 'ECONNABORTED') {
      toast.error('Request timeout. Please check your connection.')
    }
    return Promise.reject(error)
  }
)

// Auth API
export const authAPI = {
  login: async (phoneNumber: string, password: string) => {
    const response = await api.post('/auth/login', { phoneNumber, password })
    return response.data
  },
  
  verifyToken: async () => {
    const response = await api.get('/auth/verify-token')
    return response.data
  },
  
  logout: async () => {
    const response = await api.post('/auth/logout')
    return response.data
  },
}

// Users API
export const usersAPI = {
  getAll: async (page = 1, limit = 10, search = '') => {
    const response = await api.get(`/users?page=${page}&limit=${limit}&search=${search}`)
    return response.data
  },
  
  getById: async (id: string) => {
    const response = await api.get(`/users/${id}`)
    return response.data
  },
  
  update: async (id: string, data: any) => {
    const response = await api.put(`/users/${id}`, data)
    return response.data
  },

  updateUser: async (id: string, data: any) => {
    const response = await api.patch(`/users/${id}`, data)
    return response.data
  },
  
  delete: async (id: string) => {
    const response = await api.delete(`/users/${id}`)
    return response.data
  },
  
  updatePoints: async (id: string, monthlyPoints: number, yearlyPoints: number) => {
    const response = await api.put(`/users/${id}/points`, { monthlyPoints, yearlyPoints })
    return response.data
  },
}

// QR Codes API
export const qrAPI = {
  getAll: async (params: any = {}) => {
    const response = await api.get('/qrcodes', { params })
    return response.data
  },

  create: async (data: { productSize: string; quantity: number }) => {
    const response = await api.post('/qrcodes', data)
    return response.data
  },

  bulkImport: async (qrCodes: any[]) => {
    const response = await api.post('/qrcodes/bulk-import', { qrCodes })
    return response.data
  },

  getStats: async () => {
    const response = await api.get('/qrcodes/stats')
    return response.data
  },

  process: async (qrData: string) => {
    const response = await api.post('/qr/process', { qrData })
    return response.data
  },

  redeem: async (qrCode: string) => {
    const response = await api.post('/qrcodes/redeem', { qrCode })
    return response.data
  },
}

// Point History API
export const pointHistoryAPI = {
  getUserHistory: async (params: any = {}) => {
    const response = await api.get('/points/history', { params })
    return response.data
  },

  getUserStats: async (period: string = '30d') => {
    const response = await api.get('/points/stats', { params: { period } })
    return response.data
  },

  getAllHistory: async (params: any = {}) => {
    const response = await api.get('/points/history/all', { params })
    return response.data
  },

  adjustPoints: async (userId: string, pointsChange: number, reason?: string, adjustmentType: string = 'monthly') => {
    const response = await api.post('/points/adjust', { userId, pointsChange, reason, adjustmentType })
    return response.data
  },

  getAnalytics: async (period: string = '30d') => {
    const response = await api.get('/points/analytics', { params: { period } })
    return response.data
  },
}

// Contact Forms API
export const contactAPI = {
  getAll: async (params?: {
    page?: number
    limit?: number
    status?: string
    priority?: string
    search?: string
    sortBy?: string
    sortOrder?: string
  }) => {
    const queryParams = new URLSearchParams()
    if (params?.page) queryParams.append('page', params.page.toString())
    if (params?.limit) queryParams.append('limit', params.limit.toString())
    if (params?.status && params.status !== 'all') queryParams.append('status', params.status)
    if (params?.priority && params.priority !== 'all') queryParams.append('priority', params.priority)
    if (params?.search) queryParams.append('search', params.search)
    if (params?.sortBy) queryParams.append('sortBy', params.sortBy)
    if (params?.sortOrder) queryParams.append('sortOrder', params.sortOrder)

    const response = await api.get(`/contact/all?${queryParams.toString()}`)
    return response.data
  },

  getById: async (id: string) => {
    const response = await api.get(`/contact/${id}`)
    return response.data
  },

  updateStatus: async (id: string, data: {
    status?: string
    priority?: string
    adminNotes?: string
    assignedTo?: string
  }) => {
    const response = await api.put(`/contact/${id}/status`, data)
    return response.data
  },

  delete: async (id: string) => {
    const response = await api.delete(`/contact/${id}`)
    return response.data
  },

  getStats: async () => {
    const response = await api.get('/contact/stats')
    return response.data
  },
}

// Gift Redemptions API
export const giftRedemptionsAPI = {
  getPending: async (page = 1, limit = 20) => {
    const response = await api.get(`/gifts/pending?page=${page}&limit=${limit}`)
    return response.data
  },

  getAll: async (page = 1, limit = 20, status?: string) => {
    const params = new URLSearchParams({ page: page.toString(), limit: limit.toString() })
    if (status && status !== 'all') params.append('status', status)
    const response = await api.get(`/gifts/my-redemptions?${params.toString()}`)
    return response.data
  },

  process: async (id: string, action: 'approve' | 'deny', adminNotes?: string) => {
    const response = await api.put(`/gifts/process/${id}`, { action, adminNotes })
    return response.data
  },

  getById: async (id: string) => {
    const response = await api.get(`/gifts/redemption/${id}`)
    return response.data
  },

  getStats: async () => {
    const response = await api.get('/gifts/stats')
    return response.data
  },
}

// Analytics API
export const analyticsAPI = {
  getDashboardStats: async () => {
    const response = await api.get('/admin/dashboard-stats')
    return response.data
  },

  getUserStats: async (period = '30d') => {
    const response = await api.get(`/admin/user-analytics?period=${period}`)
    return response.data
  },

  getPointsStats: async (period = '30d') => {
    const response = await api.get(`/admin/points-analytics?period=${period}`)
    return response.data
  },

  getQRStats: async () => {
    const response = await api.get('/admin/qr-stats')
    return response.data
  },

  getSystemHealth: async () => {
    const response = await api.get('/admin/system-health')
    return response.data
  },

  exportUsers: async (format = 'json') => {
    const response = await api.get(`/admin/export-users?format=${format}`)
    return response.data
  },

  bulkUpdateStatus: async (userIds: string[], status: string) => {
    const response = await api.post('/admin/bulk-update-status', { userIds, status })
    return response.data
  },
}

// Recharge API
export const rechargeAPI = {
  getAll: async (page = 1, limit = 20, status?: string) => {
    const params = new URLSearchParams({ page: page.toString(), limit: limit.toString() })
    if (status && status !== 'all') params.append('status', status)
    const response = await api.get(`/recharges/admin?${params.toString()}`)
    return response.data
  },

  updateStatus: async (id: string, status: 'completed' | 'denied', adminNotes?: string) => {
    const response = await api.patch(`/recharges/admin/${id}`, {
      status,
      adminNotes,
      processedBy: 'admin_user_id' // TODO: Get from auth context
    })
    return response.data
  },

  getStats: async () => {
    const response = await api.get('/recharges/stats')
    return response.data
  },
}

export default api
